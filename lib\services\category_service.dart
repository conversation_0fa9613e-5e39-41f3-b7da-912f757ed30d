import 'package:flutter/foundation.dart' hide Category;
import 'package:hive/hive.dart';
import 'package:my_fincance_app/models/category.dart';
import 'package:uuid/uuid.dart';

class CategoryService extends ChangeNotifier {
  final Box<Category> _categoryBox = Hive.box<Category>('categories');
  final String _userId; // Assuming a single user for now
  final _uuid = Uuid();

  List<Category> _categories = [];

  CategoryService(this._userId) {
    _loadCategories();
    _initializeDefaultCategories();
  }

  List<Category> get categories => _categories;

  void _loadCategories() {
    _categories = _categoryBox.values.where((c) => c.userId == _userId).toList();
    notifyListeners();
  }

  void addCategory(String name, String type) {
    final newCategory = Category()
      ..id = _uuid.v4()
      ..name = name
      ..type = type
      ..userId = _userId;
    _categoryBox.put(newCategory.id, newCategory);
    _loadCategories();
  }

  void updateCategory(Category category, String newName, String newType) {
    category.name = newName;
    category.type = newType;
    category.save();
    _loadCategories();
  }

  void deleteCategory(Category category) {
    category.delete();
    _loadCategories();
  }

  void _initializeDefaultCategories() {
    if (_categories.isEmpty && _userId.isNotEmpty) {
      // Default income categories
      final defaultIncomeCategories = [
        'معاش',
        'کسب و کار',
        'سرمایه‌گذاری',
        'هدیه',
        'سایر درآمدها',
      ];

      // Default expense categories
      final defaultExpenseCategories = [
        'غذا و نوشیدنی',
        'حمل و نقل',
        'خرید',
        'تفریح',
        'بهداشت و درمان',
        'آموزش',
        'خانه',
        'لباس',
        'سایر مصارف',
      ];

      // Add default income categories
      for (final categoryName in defaultIncomeCategories) {
        final category = Category()
          ..id = _uuid.v4()
          ..name = categoryName
          ..type = 'درآمد'
          ..userId = _userId;
        _categoryBox.put(category.id, category);
      }

      // Add default expense categories
      for (final categoryName in defaultExpenseCategories) {
        final category = Category()
          ..id = _uuid.v4()
          ..name = categoryName
          ..type = 'مصرف'
          ..userId = _userId;
        _categoryBox.put(category.id, category);
      }

      _loadCategories();
    }
  }
}
