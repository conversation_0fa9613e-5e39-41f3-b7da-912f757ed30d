import 'package:hive/hive.dart';

part 'transaction.g.dart';

@HiveType(typeId: 3)
class Transaction extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late DateTime date;

  @HiveField(2)
  late String description;

  @HiveField(3)
  late double amount;

  @HiveField(4)
  late String type; // "درآمد" or "مصرف"

  @HiveField(5)
  late String categoryId;

  @HiveField(6)
  String? loanId;

  @HiveField(7)
  late String userId;
}
