import 'package:flutter/material.dart';
import 'package:my_fincance_app/models/category.dart';
import 'package:my_fincance_app/services/auth_service.dart';
import 'package:my_fincance_app/services/category_service.dart';
import 'package:my_fincance_app/pages/auth/login_page.dart';
import 'package:my_fincance_app/pages/help_page.dart';
import 'package:provider/provider.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  int _selectedTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تنظیمات'),
        centerTitle: true,
        backgroundColor: Colors.blue.shade50,
        elevation: 0,
        bottom: TabBar(
          controller: null,
          labelColor: Colors.blue,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Colors.blue,
          onTap: (index) {
            setState(() {
              _selectedTabIndex = index;
            });
          },
          tabs: const [
            Tab(
              icon: Icon(Icons.person),
              text: 'پروفایل',
            ),
            Tab(
              icon: Icon(Icons.folder),
              text: 'دسته‌بندی‌ها',
            ),
          ],
        ),
      ),
      backgroundColor: Colors.grey.shade50,
      body: IndexedStack(
        index: _selectedTabIndex,
        children: [
          _buildProfileTab(),
          _buildCategoriesTab(),
        ],
      ),
      floatingActionButton: _selectedTabIndex == 1
          ? FloatingActionButton.extended(
              onPressed: () {
                _showCategoryDialog(context, Provider.of<CategoryService>(context, listen: false));
              },
              icon: const Icon(Icons.add),
              label: const Text('دسته جدید'),
              backgroundColor: Colors.blue,
            )
          : null,
    );
  }

  Widget _buildProfileTab() {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        final user = authService.currentUser;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Profile card
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      CircleAvatar(
                        radius: 50,
                        backgroundColor: Colors.blue.withValues(alpha: 0.1),
                        child: const Icon(
                          Icons.person,
                          size: 50,
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        user?.name ?? 'کاربر',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        user?.email ?? '',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Settings options
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Column(
                  children: [
                    ListTile(
                      leading: const Icon(Icons.edit, color: Colors.blue),
                      title: const Text('ویرایش پروفایل'),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _showEditProfileDialog(context, authService),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: const Icon(Icons.lock, color: Colors.orange),
                      title: const Text('تغییر رمز عبور'),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _showChangePasswordDialog(context, authService),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: const Icon(Icons.help, color: Colors.green),
                      title: const Text('راهنمای کاربر'),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(builder: (context) => const HelpPage()),
                        );
                      },
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: const Icon(Icons.info, color: Colors.green),
                      title: const Text('درباره برنامه'),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _showAboutDialog(context),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: const Icon(Icons.logout, color: Colors.red),
                      title: const Text('خروج از حساب'),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _showLogoutDialog(context, authService),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCategoriesTab() {
    return Consumer<CategoryService>(
      builder: (context, categoryService, child) {
        final incomeCategories = categoryService.categories.where((c) => c.type == 'درآمد').toList();
        final expenseCategories = categoryService.categories.where((c) => c.type == 'مصرف').toList();

        if (categoryService.categories.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.folder_open,
                  size: 80,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'هنوز دسته‌بندی‌ای تعریف نشده است',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'برای شروع، دسته‌بندی جدید اضافه کنید',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Income categories
              if (incomeCategories.isNotEmpty) ...[
                _buildCategorySectionHeader('دسته‌های درآمد', Colors.green, incomeCategories.length),
                const SizedBox(height: 12),
                ...incomeCategories.map((category) => _buildCategoryCard(category, categoryService)),
                const SizedBox(height: 20),
              ],

              // Expense categories
              if (expenseCategories.isNotEmpty) ...[
                _buildCategorySectionHeader('دسته‌های مصرف', Colors.red, expenseCategories.length),
                const SizedBox(height: 12),
                ...expenseCategories.map((category) => _buildCategoryCard(category, categoryService)),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildCategorySectionHeader(String title, Color color, int count) {
    return Row(
      children: [
        Icon(Icons.folder, color: color),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(width: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            count.toString(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryCard(Category category, CategoryService categoryService) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: category.type == 'درآمد'
              ? Colors.green.withValues(alpha: 0.1)
              : Colors.red.withValues(alpha: 0.1),
          child: Icon(
            category.type == 'درآمد' ? Icons.trending_up : Icons.trending_down,
            color: category.type == 'درآمد' ? Colors.green : Colors.red,
          ),
        ),
        title: Text(
          category.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(category.type),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            if (value == 'edit') {
              _showCategoryDialog(context, categoryService, category: category);
            } else if (value == 'delete') {
              _showDeleteCategoryDialog(context, categoryService, category);
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('ویرایش'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCategoryDialog(BuildContext context, CategoryService categoryService, {Category? category}) {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController(text: category?.name ?? '');
    String selectedType = category?.type ?? 'مصرف';

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              title: Row(
                children: [
                  Icon(
                    category == null ? Icons.add : Icons.edit,
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  Text(category == null ? 'افزودن دسته جدید' : 'ویرایش دسته'),
                ],
              ),
              content: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: 'نام دسته',
                        prefixIcon: Icon(Icons.label),
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'لطفاً نام دسته را وارد کنید';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: selectedType,
                      decoration: const InputDecoration(
                        labelText: 'نوع دسته',
                        prefixIcon: Icon(Icons.category),
                        border: OutlineInputBorder(),
                      ),
                      items: [
                        const DropdownMenuItem(
                          value: 'درآمد',
                          child: Row(
                            children: [
                              Icon(Icons.trending_up, color: Colors.green),
                              SizedBox(width: 8),
                              Text('درآمد'),
                            ],
                          ),
                        ),
                        const DropdownMenuItem(
                          value: 'مصرف',
                          child: Row(
                            children: [
                              Icon(Icons.trending_down, color: Colors.red),
                              SizedBox(width: 8),
                              Text('مصرف'),
                            ],
                          ),
                        ),
                      ],
                      onChanged: (newValue) {
                        setState(() {
                          selectedType = newValue!;
                        });
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('لغو'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (formKey.currentState!.validate()) {
                      if (category == null) {
                        categoryService.addCategory(nameController.text.trim(), selectedType);
                      } else {
                        categoryService.updateCategory(category, nameController.text.trim(), selectedType);
                      }
                      Navigator.of(context).pop();

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(category == null ? 'دسته جدید با موفقیت ثبت شد' : 'دسته با موفقیت ویرایش شد'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  },
                  child: Text(category == null ? 'ثبت' : 'ویرایش'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showDeleteCategoryDialog(BuildContext context, CategoryService categoryService, Category category) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Row(
            children: [
              Icon(Icons.warning, color: Colors.red),
              SizedBox(width: 8),
              Text('حذف دسته'),
            ],
          ),
          content: Text('آیا مطمئن هستید که می‌خواهید دسته "${category.name}" را حذف کنید؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('لغو'),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              onPressed: () {
                categoryService.deleteCategory(category);
                Navigator.of(context).pop();

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('دسته با موفقیت حذف شد'),
                    backgroundColor: Colors.red,
                  ),
                );
              },
              child: const Text('حذف', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }

  void _showEditProfileDialog(BuildContext context, AuthService authService) {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController(text: authService.currentUser?.name ?? '');

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Row(
            children: [
              Icon(Icons.edit, color: Colors.blue),
              SizedBox(width: 8),
              Text('ویرایش پروفایل'),
            ],
          ),
          content: Form(
            key: formKey,
            child: TextFormField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'نام کامل',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'لطفاً نام خود را وارد کنید';
                }
                if (value.length < 2) {
                  return 'نام باید حداقل ۲ کاراکتر باشد';
                }
                return null;
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('لغو'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  final success = await authService.updateProfile(nameController.text.trim());
                  Navigator.of(context).pop();

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(success ? 'پروفایل با موفقیت ویرایش شد' : 'خطا در ویرایش پروفایل'),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                }
              },
              child: const Text('ذخیره'),
            ),
          ],
        );
      },
    );
  }

  void _showChangePasswordDialog(BuildContext context, AuthService authService) {
    final formKey = GlobalKey<FormState>();
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Row(
            children: [
              Icon(Icons.lock, color: Colors.orange),
              SizedBox(width: 8),
              Text('تغییر رمز عبور'),
            ],
          ),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: currentPasswordController,
                  obscureText: true,
                  decoration: const InputDecoration(
                    labelText: 'رمز عبور فعلی',
                    prefixIcon: Icon(Icons.lock_outline),
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'لطفاً رمز عبور فعلی را وارد کنید';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: newPasswordController,
                  obscureText: true,
                  decoration: const InputDecoration(
                    labelText: 'رمز عبور جدید',
                    prefixIcon: Icon(Icons.lock),
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'لطفاً رمز عبور جدید را وارد کنید';
                    }
                    if (value.length < 6) {
                      return 'رمز عبور باید حداقل ۶ کاراکتر باشد';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: confirmPasswordController,
                  obscureText: true,
                  decoration: const InputDecoration(
                    labelText: 'تکرار رمز عبور جدید',
                    prefixIcon: Icon(Icons.lock_outline),
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'لطفاً رمز عبور را تکرار کنید';
                    }
                    if (value != newPasswordController.text) {
                      return 'رمز عبور و تکرار آن یکسان نیستند';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('لغو'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  final success = await authService.changePassword(
                    currentPasswordController.text,
                    newPasswordController.text,
                  );
                  Navigator.of(context).pop();

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(success ? 'رمز عبور با موفقیت تغییر کرد' : 'رمز عبور فعلی اشتباه است'),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                }
              },
              child: const Text('تغییر'),
            ),
          ],
        );
      },
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Row(
            children: [
              Icon(Icons.info, color: Colors.green),
              SizedBox(width: 8),
              Text('درباره برنامه'),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'مرکز مالی من',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('نسخه ۱.۰.۰'),
              SizedBox(height: 16),
              Text(
                'برنامه جامع مدیریت مالی شخصی با قابلیت‌های:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• مدیریت درآمد و مصارف'),
              Text('• مدیریت وام‌ها و طلب‌ها'),
              Text('• بودجه‌بندی ماهانه'),
              Text('• گزارش‌گیری و نمودارها'),
              Text('• رابط کاربری فارسی'),
              Text('• واحد پولی افغانی'),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('بستن'),
            ),
          ],
        );
      },
    );
  }

  void _showLogoutDialog(BuildContext context, AuthService authService) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Row(
            children: [
              Icon(Icons.logout, color: Colors.red),
              SizedBox(width: 8),
              Text('خروج از حساب'),
            ],
          ),
          content: const Text('آیا مطمئن هستید که می‌خواهید از حساب کاربری خود خارج شوید؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('لغو'),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              onPressed: () {
                authService.logout();
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) => const LoginPage()),
                  (route) => false,
                );
              },
              child: const Text('خروج', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }
}