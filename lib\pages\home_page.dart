import 'package:flutter/material.dart';
import 'package:my_fincance_app/pages/dashboard_page.dart';
import 'package:my_fincance_app/pages/loans_page.dart';
import 'package:my_fincance_app/pages/settings_page.dart';
import 'package:my_fincance_app/pages/transactions_page.dart';
import 'package:my_fincance_app/pages/budgets_page.dart';
import 'package:my_fincance_app/widgets/financial_summary_widget.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;

  static const List<Widget> _widgetOptions = <Widget>[
    DashboardPage(),
    LoansPage(),
    TransactionsPage(),
    BudgetsPage(),
    SettingsPage(),
  ];

  static const List<Widget> _summaryWidgets = <Widget>[
    FinancialSummaryWidget(),
    SizedBox.shrink(),
    SizedBox.shrink(),
    SizedBox.shrink(),
    SizedBox.shrink(),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _summaryWidgets.elementAt(_selectedIndex),
          Expanded(
            child: _widgetOptions.elementAt(_selectedIndex),
          ),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'داشبورد',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_balance_wallet),
            label: 'وام‌ها',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.transform),
            label: 'تراکنش‌ها',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.pie_chart),
            label: 'بودجه‌بندی',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'تنظیمات',
          ),
        ],
        currentIndex: _selectedIndex,
        selectedItemColor: Colors.blue,
        unselectedItemColor: Colors.grey,
        onTap: _onItemTapped,
      ),
    );
  }
}
