import 'package:flutter/material.dart';

class HelpPage extends StatefulWidget {
  const HelpPage({super.key});

  @override
  State<HelpPage> createState() => _HelpPageState();
}

class _HelpPageState extends State<HelpPage> {
  int _selectedIndex = 0;

  final List<HelpSection> _helpSections = [
    HelpSection(
      title: 'شروع کار',
      icon: Icons.play_arrow,
      items: [
        HelpItem(
          title: 'ثبت نام و ورود',
          content: 'برای شروع کار با برنامه، ابتدا باید حساب کاربری ایجاد کنید. پس از ثبت نام، می‌توانید با ایمیل و رمز عبور خود وارد شوید.',
        ),
        HelpItem(
          title: 'تنظیم دسته‌بندی‌ها',
          content: 'برنامه به طور خودکار دسته‌بندی‌های پایه را ایجاد می‌کند. می‌توانید در بخش تنظیمات، دسته‌بندی‌های جدید اضافه کنید یا موجودی‌ها را ویرایش کنید.',
        ),
        HelpItem(
          title: 'آشنایی با رابط کاربری',
          content: 'برنامه دارای ۵ بخش اصلی است: داشبورد، وام‌ها، تراکنش‌ها، بودجه‌بندی و تنظیمات. هر بخش برای مدیریت جنبه خاصی از امور مالی شما طراحی شده است.',
        ),
      ],
    ),
    HelpSection(
      title: 'مدیریت تراکنش‌ها',
      icon: Icons.receipt_long,
      items: [
        HelpItem(
          title: 'افزودن تراکنش جدید',
          content: 'برای افزودن تراکنش، روی دکمه "+" در صفحه تراکنش‌ها کلیک کنید. توضیحات، مبلغ، نوع (درآمد/مصرف) و دسته‌بندی را وارد کنید.',
        ),
        HelpItem(
          title: 'ویرایش و حذف تراکنش',
          content: 'برای ویرایش یا حذف تراکنش، روی منوی سه نقطه در کنار هر تراکنش کلیک کنید و گزینه مورد نظر را انتخاب کنید.',
        ),
        HelpItem(
          title: 'فیلتر کردن تراکنش‌ها',
          content: 'می‌توانید تراکنش‌ها را بر اساس نوع، دسته‌بندی و بازه زمانی فیلتر کنید. از آیکون فیلتر در بالای صفحه استفاده کنید.',
        ),
        HelpItem(
          title: 'ربط دادن به وام',
          content: 'هنگام افزودن تراکنش، می‌توانید آن را به وام خاصی ربط دهید. این کار به شما کمک می‌کند تا پیشرفت پرداخت وام‌ها را دنبال کنید.',
        ),
      ],
    ),
    HelpSection(
      title: 'مدیریت وام‌ها',
      icon: Icons.account_balance_wallet,
      items: [
        HelpItem(
          title: 'انواع وام',
          content: 'دو نوع وام وجود دارد: بدهی (پولی که شما بدهکار هستید) و طلب (پولی که به شما بدهکار هستند).',
        ),
        HelpItem(
          title: 'افزودن وام جدید',
          content: 'برای افزودن وام، نام وام، نوع، نام شخص/موسسه، مبلغ کل و تاریخ شروع را وارد کنید.',
        ),
        HelpItem(
          title: 'پیگیری پیشرفت وام',
          content: 'برنامه به طور خودکار پیشرفت پرداخت وام‌ها را محاسبه می‌کند. وقتی وامی کاملاً پرداخت شود، وضعیت آن به "تمام شده" تغییر می‌کند.',
        ),
        HelpItem(
          title: 'مشاهده جزئیات وام',
          content: 'با کلیک روی هر وام، می‌توانید جزئیات کامل آن شامل تاریخچه پرداخت‌ها را مشاهده کنید.',
        ),
      ],
    ),
    HelpSection(
      title: 'بودجه‌بندی',
      icon: Icons.pie_chart,
      items: [
        HelpItem(
          title: 'تعریف بودجه ماهانه',
          content: 'برای هر دسته‌بندی مصرف، می‌توانید بودجه ماهانه تعریف کنید. این کار به شما کمک می‌کند مصارف خود را کنترل کنید.',
        ),
        HelpItem(
          title: 'پیگیری بودجه',
          content: 'برنامه مصرف واقعی شما را با بودجه تعریف شده مقایسه می‌کند و در صورت تجاوز از بودجه، هشدار می‌دهد.',
        ),
        HelpItem(
          title: 'نمودار بودجه',
          content: 'در داشبورد می‌توانید نمودار مقایسه بودجه و مصرف واقعی را مشاهده کنید.',
        ),
      ],
    ),
    HelpSection(
      title: 'داشبورد و گزارش‌ها',
      icon: Icons.dashboard,
      items: [
        HelpItem(
          title: 'خلاصه مالی',
          content: 'در تب خلاصه مالی، اطلاعات کلی درآمد، مصرف، پس‌انداز و وضعیت وام‌های شما نمایش داده می‌شود.',
        ),
        HelpItem(
          title: 'گزارش ماهانه',
          content: 'تب گزارش ماهانه شامل نمودارهای تفصیلی مصارف و مقایسه بودجه است.',
        ),
        HelpItem(
          title: 'سرمایه کل',
          content: 'در تب سرمایه کل، ارزش خالص دارایی شما (درآمدها منهای مصارف و بدهی‌ها) نمایش داده می‌شود.',
        ),
      ],
    ),
    HelpSection(
      title: 'تنظیمات',
      icon: Icons.settings,
      items: [
        HelpItem(
          title: 'مدیریت پروفایل',
          content: 'می‌توانید نام خود را ویرایش کنید، رمز عبور را تغییر دهید یا از حساب خود خارج شوید.',
        ),
        HelpItem(
          title: 'مدیریت دسته‌بندی‌ها',
          content: 'دسته‌بندی‌های جدید اضافه کنید، موجودی‌ها را ویرایش کنید یا حذف کنید.',
        ),
      ],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('راهنمای کاربر'),
        centerTitle: true,
        backgroundColor: Colors.blue.shade50,
        elevation: 0,
      ),
      backgroundColor: Colors.grey.shade50,
      body: Row(
        children: [
          // Sidebar
          Container(
            width: 250,
            color: Colors.white,
            child: ListView.builder(
              itemCount: _helpSections.length,
              itemBuilder: (context, index) {
                final section = _helpSections[index];
                final isSelected = index == _selectedIndex;
                
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.blue.withValues(alpha: 0.1) : null,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListTile(
                    leading: Icon(
                      section.icon,
                      color: isSelected ? Colors.blue : Colors.grey,
                    ),
                    title: Text(
                      section.title,
                      style: TextStyle(
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        color: isSelected ? Colors.blue : Colors.black,
                      ),
                    ),
                    onTap: () {
                      setState(() {
                        _selectedIndex = index;
                      });
                    },
                  ),
                );
              },
            ),
          ),
          
          // Content
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    final section = _helpSections[_selectedIndex];
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(section.icon, size: 32, color: Colors.blue),
              const SizedBox(width: 12),
              Text(
                section.title,
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          
          ...section.items.map((item) => _buildHelpItem(item)),
        ],
      ),
    );
  }

  Widget _buildHelpItem(HelpItem item) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              item.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              item.content,
              style: const TextStyle(
                fontSize: 16,
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class HelpSection {
  final String title;
  final IconData icon;
  final List<HelpItem> items;

  HelpSection({
    required this.title,
    required this.icon,
    required this.items,
  });
}

class HelpItem {
  final String title;
  final String content;

  HelpItem({
    required this.title,
    required this.content,
  });
}
