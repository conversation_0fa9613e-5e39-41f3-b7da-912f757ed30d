import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:my_fincance_app/services/transaction_service.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:my_fincance_app/services/budget_service.dart';
import 'package:my_fincance_app/utils/currency_formatter.dart';
import 'package:my_fincance_app/utils/date_formatter.dart';

class FinancialSummaryWidget extends StatelessWidget {
  const FinancialSummaryWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer3<TransactionService, LoanService, BudgetService>(
      builder: (context, transactionService, loanService, budgetService, child) {
        // Financial metrics
        final totalIncome = transactionService.totalCurrentMonthIncome;
        final totalExpenses = transactionService.totalCurrentMonthExpenses;
        final netSavings = transactionService.currentMonthNetSavings;
        final totalActiveDebts = loanService.getTotalActiveDebt();
        final totalActiveCredits = loanService.getTotalActiveCredit();
        
        // Budget metrics
        final budgetVsActual = budgetService.getCurrentMonthBudgetVsActual();
        final totalBudget = budgetVsActual.values.fold<double>(0.0, (sum, data) => sum + data['budget']!);
        final budgetRemaining = totalBudget - totalExpenses;

        return Container(
          margin: const EdgeInsets.all(16),
          child: Card(
            elevation: 8,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  colors: [
                    Colors.blue.withValues(alpha: 0.1),
                    Colors.blue.withValues(alpha: 0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        const Icon(
                          Icons.account_balance_wallet,
                          size: 32,
                          color: Colors.blue,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'خلاصه مالی',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                DateFormatter.getCurrentMonthYear(),
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: netSavings >= 0 ? Colors.green : Colors.red,
                            borderRadius: BorderRadius.circular(25),
                          ),
                          child: Text(
                            netSavings >= 0 ? 'سودآور' : 'زیان‌ده',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Main financial metrics
                    Row(
                      children: [
                        Expanded(
                          child: _buildMetricCard(
                            'درآمد ماه',
                            totalIncome,
                            Colors.green,
                            Icons.trending_up,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildMetricCard(
                            'مصرف ماه',
                            totalExpenses,
                            Colors.red,
                            Icons.trending_down,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    
                    _buildMetricCard(
                      'پس‌انداز خالص',
                      netSavings,
                      netSavings >= 0 ? Colors.blue : Colors.orange,
                      netSavings >= 0 ? Icons.savings : Icons.warning,
                      isFullWidth: true,
                    ),
                    
                    if (totalBudget > 0) ...[
                      const SizedBox(height: 12),
                      _buildMetricCard(
                        'باقیمانده بودجه',
                        budgetRemaining,
                        budgetRemaining >= 0 ? Colors.green : Colors.red,
                        budgetRemaining >= 0 ? Icons.check_circle : Icons.error,
                        isFullWidth: true,
                      ),
                    ],

                    // Loan summary
                    if (totalActiveDebts > 0 || totalActiveCredits > 0) ...[
                      const SizedBox(height: 20),
                      const Divider(),
                      const SizedBox(height: 16),
                      const Text(
                        'وضعیت وام‌ها',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          if (totalActiveDebts > 0)
                            Expanded(
                              child: _buildMetricCard(
                                'بدهی‌ها',
                                totalActiveDebts,
                                Colors.orange,
                                Icons.money_off,
                              ),
                            ),
                          if (totalActiveDebts > 0 && totalActiveCredits > 0)
                            const SizedBox(width: 12),
                          if (totalActiveCredits > 0)
                            Expanded(
                              child: _buildMetricCard(
                                'طلب‌ها',
                                totalActiveCredits,
                                Colors.purple,
                                Icons.account_balance_wallet,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMetricCard(
    String label,
    double amount,
    Color color,
    IconData icon, {
    bool isFullWidth = false,
  }) {
    final card = Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            CurrencyFormatter.formatWithPersianDigits(amount),
            style: TextStyle(
              fontSize: isFullWidth ? 18 : 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );

    return isFullWidth ? card : Expanded(child: card);
  }
}
